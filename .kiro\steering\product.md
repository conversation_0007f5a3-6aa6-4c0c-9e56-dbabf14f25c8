# Product Overview

## AMIS Five - Agricultural Management Information System

AMIS Five is a comprehensive agricultural management system built for Papua New Guinea's Department of Agriculture and Livestock. The system manages workplans, activities, proposals, reports, and agricultural data across government administrative structures.

## Core Purpose

- **Workplan Management**: Central planning for agricultural activities with supervisor assignment and status tracking
- **Activity Tracking**: Four types of activities (training, infrastructure, inputs, output) with GPS coordinates and implementation tracking
- **Proposal Workflow**: Activity approval system with supervisor and M&E rating capabilities
- **Government Integration**: Manages PNG's hierarchical government structure (Province → District → LLG → Ward)
- **Planning Framework Integration**: Links activities to NASP, MTDP, Corporate Plans, and other frameworks
- **Document Management**: File and folder system with access control
- **Reporting**: Comprehensive reporting with charts, maps, and data visualization

## Key Users

- **Admin**: Full system access and user management
- **Supervisors**: Workplan supervision and proposal approval
- **Standard Users**: Activity creation and management within their scope
- **M&E Evaluators**: Activity rating and evaluation
- **Commodity Board Users**: Specialized commodity management access

## System Characteristics

- Production-ready system actively used by government departments
- Role-based access control with hierarchical reporting structures
- Email notification system for workflow events
- GPS mapping integration with OpenStreetMap
- File upload and document management capabilities
- Responsive web interface optimized for government workflows