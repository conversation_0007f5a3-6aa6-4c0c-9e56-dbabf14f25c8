<?php
// app/Views/documents/documents_folder_edit.php
?>
<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb bg-white p-3 mb-4 rounded shadow-sm">
            <li class="breadcrumb-item"><a href="<?= base_url('documents') ?>"><i class="fas fa-folder-open"></i> Documents</a></li>
            <?php foreach ($folder_path as $index => $path_item): ?>
                <?php if ($index === count($folder_path) - 1): ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($path_item['name']) ?></li>
                <?php else: ?>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('documents?parent_id=' . $path_item['id']) ?>">
                            <?= esc($path_item['name']) ?>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i> Edit Folder</h5>
                </div>
                <div class="card-body">
                    <?php if (session()->has('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session('errors') as $error): ?>
                                    <li><?= $error ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="<?= base_url('documents/update/' . $folder['id']) ?>" method="post">
                        <?= csrf_field() ?>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Folder Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?= (isset(session('errors')['name'])) ? 'is-invalid' : '' ?>" 
                                id="name" name="name" value="<?= old('name', $folder['name']) ?>" required>
                            <?php if (isset(session('errors')['name'])): ?>
                                <div class="invalid-feedback"><?= session('errors')['name'] ?></div>
                            <?php endif; ?>
                        </div>

                        <?php if ($user_branch_id): ?>
                            <div class="mb-3">
                                <label class="form-label">Branch</label>
                                <div class="form-control-plaintext bg-light p-2 rounded">
                                    <i class="fas fa-building me-2"></i>
                                    <span class="text-muted">Automatically assigned to your branch</span>
                                </div>
                                <input type="hidden" name="branch_id" value="<?= $user_branch_id ?>">
                            </div>
                        <?php else: ?>
                            <div class="mb-3">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No branch assigned to your account. Please contact administrator.
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control <?= (isset(session('errors')['description'])) ? 'is-invalid' : '' ?>" 
                                id="description" name="description" rows="3"><?= old('description', $folder['description']) ?></textarea>
                            <?php if (isset(session('errors')['description'])): ?>
                                <div class="invalid-feedback"><?= session('errors')['description'] ?></div>
                            <?php endif; ?>
                            <div class="form-text">Briefly describe the purpose of this folder.</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Access Level <span class="text-danger">*</span></label>
                            <div class="d-flex gap-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="access" id="access_private" 
                                        value="private" <?= old('access', $folder['access']) == 'private' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="access_private">
                                        <span class="badge bg-danger">Private</span>
                                        <small class="d-block text-muted">Only you can view</small>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="access" id="access_internal" 
                                        value="internal" <?= old('access', $folder['access']) == 'internal' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="access_internal">
                                        <span class="badge bg-warning text-dark">Internal</span>
                                        <small class="d-block text-muted">Only staff can view</small>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="access" id="access_public" 
                                        value="public" <?= old('access', $folder['access']) == 'public' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="access_public">
                                        <span class="badge bg-success">Public</span>
                                        <small class="d-block text-muted">Everyone can view</small>
                                    </label>
                                </div>
                            </div>
                            <?php if (isset(session('errors')['access'])): ?>
                                <div class="text-danger mt-1"><?= session('errors')['access'] ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <?php if ($folder['parent_folder_id']): ?>
                                <a href="<?= base_url('documents?parent_id=' . $folder['parent_folder_id']) ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Parent Folder
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('documents') ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Documents
                                </a>
                            <?php endif; ?>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Update Folder
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?> 