<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?= $title ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-left: 4px solid #007bff;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        .stats-row {
            display: table-row;
        }
        .stats-cell {
            display: table-cell;
            width: 25%;
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
        }
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stats-label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .badge {
            background-color: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .badge-success { background-color: #28a745; }
        .badge-warning { background-color: #ffc107; color: #212529; }
        .badge-danger { background-color: #dc3545; }
        .badge-info { background-color: #17a2b8; }
        .footer {
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 10px;
            color: #666;
        }
        .two-column {
            display: table;
            width: 100%;
        }
        .column {
            display: table-cell;
            width: 50%;
            padding-right: 10px;
            vertical-align: top;
        }
        .column:last-child {
            padding-right: 0;
            padding-left: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><?= $title ?></h1>
        <p>PNG Administrative Hierarchy Analytics Report</p>
        <p>Generated on: <?= $generated_at ?> | Generated by: <?= $generated_by ?></p>
    </div>

    <!-- Hierarchy Overview Section -->
    <div class="section">
        <div class="section-title">Administrative Hierarchy Overview</div>
        
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <div class="stats-number"><?= $hierarchyData['provinces'] ?></div>
                    <div class="stats-label">Provinces</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-number"><?= $hierarchyData['districts'] ?></div>
                    <div class="stats-label">Districts</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-number"><?= $hierarchyData['llgs'] ?></div>
                    <div class="stats-label">LLGs</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-number"><?= $hierarchyData['wards'] ?></div>
                    <div class="stats-label">Wards</div>
                </div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Province</th>
                    <th>Districts</th>
                    <th>LLGs</th>
                    <th>Wards</th>
                    <th>Total Units</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($hierarchyData['provinceBreakdown'] as $province): ?>
                <tr>
                    <td><?= esc($province['province']['name']) ?></td>
                    <td><span class="badge"><?= $province['districts'] ?></span></td>
                    <td><span class="badge badge-success"><?= $province['llgs'] ?></span></td>
                    <td><span class="badge badge-warning"><?= $province['wards'] ?></span></td>
                    <td><strong><?= $province['total'] ?></strong></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Activity Distribution Section -->
    <div class="section">
        <div class="section-title">Activity Distribution Analysis</div>
        
        <div class="two-column">
            <div class="column">
                <h4>Activities by Province</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Province</th>
                            <th>Code</th>
                            <th>Activities</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($activityDistribution['byProvince'] as $province): ?>
                        <tr>
                            <td><?= esc($province['province_name']) ?></td>
                            <td><?= esc($province['province_code']) ?></td>
                            <td><span class="badge badge-info"><?= $province['activity_count'] ?></span></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="column">
                <h4>Activities by Type</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Activity Type</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($activityDistribution['byType'] as $type): ?>
                        <tr>
                            <td><?= ucfirst(esc($type['activity_type'])) ?></td>
                            <td><span class="badge badge-info"><?= $type['activity_count'] ?></span></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Coverage Analysis Section -->
    <div class="section">
        <div class="section-title">Coverage Analysis</div>
        
        <div class="two-column">
            <div class="column">
                <h4>Province Coverage</h4>
                <table>
                    <tr>
                        <td>Provinces with Activities</td>
                        <td><span class="badge badge-success"><?= $coverageAnalysis['provinceCoverage']['withActivities'] ?></span></td>
                    </tr>
                    <tr>
                        <td>Provinces without Activities</td>
                        <td><span class="badge badge-danger"><?= $coverageAnalysis['provinceCoverage']['withoutActivities'] ?></span></td>
                    </tr>
                </table>
            </div>
            
            <div class="column">
                <h4>District Coverage</h4>
                <table>
                    <tr>
                        <td>Districts with Activities</td>
                        <td><span class="badge badge-success"><?= $coverageAnalysis['districtCoverage']['withActivities'] ?></span></td>
                    </tr>
                    <tr>
                        <td>Districts without Activities</td>
                        <td><span class="badge badge-danger"><?= $coverageAnalysis['districtCoverage']['withoutActivities'] ?></span></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Administrative Efficiency Section -->
    <div class="section">
        <div class="section-title">Administrative Efficiency</div>
        
        <table>
            <thead>
                <tr>
                    <th>Province</th>
                    <th>Total Units</th>
                    <th>Activities</th>
                    <th>Activity Density</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($efficiencyData['provinceEfficiency'] as $province): ?>
                <tr>
                    <td><?= esc($province['province_name']) ?></td>
                    <td><?= $province['total_units'] ?></td>
                    <td><span class="badge badge-info"><?= $province['activity_count'] ?></span></td>
                    <td><strong><?= $province['activity_density'] ?></strong></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Most Active Areas Section -->
    <div class="section">
        <div class="section-title">Most Active Areas (Top 10)</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Province</th>
                    <th>District</th>
                    <th>Activity Count</th>
                </tr>
            </thead>
            <tbody>
                <?php $rank = 1; ?>
                <?php foreach ($efficiencyData['mostActive'] as $area): ?>
                <tr>
                    <td><?= $rank ?></td>
                    <td><?= esc($area['province_name']) ?></td>
                    <td><?= esc($area['district_name']) ?></td>
                    <td><span class="badge badge-success"><?= $area['activity_count'] ?></span></td>
                </tr>
                <?php $rank++; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report was automatically generated by the AMIS Government Structure Analytics module.</p>
        <p>For more information, please contact the system administrator.</p>
    </div>
</body>
</html>
