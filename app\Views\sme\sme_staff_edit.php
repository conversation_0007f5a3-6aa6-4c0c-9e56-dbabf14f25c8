<?php
// app/Views/sme/sme_staff_edit.php
?>
<?= $this->extend('templates/system_template') ?>
<?= $this->section('content') ?>
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Edit Staff Member</h4>
            <a href="<?= base_url('smes/staff/' . $sme_id) ?>" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Staff List
            </a>
        </div>
        <div class="card-body">
            <form action="<?= base_url('smes/staff/' . $sme_id . '/' . $staff['id']) ?>" method="post" enctype="multipart/form-data">
                <?= csrf_field() ?>

                <!-- Personal Information -->
                <h5 class="border-bottom pb-2 mb-3">Personal Information</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label" for="fname">First Name <span class="text-danger">*</span></label>
                        <input type="text" id="fname" name="fname" class="form-control" value="<?= esc($staff['fname']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="lname">Last Name <span class="text-danger">*</span></label>
                        <input type="text" id="lname" name="lname" class="form-control" value="<?= esc($staff['lname']) ?>" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label" for="gender">Gender <span class="text-danger">*</span></label>
                        <select id="gender" name="gender" class="form-select" required>
                            <option value="">Select Gender</option>
                            <option value="male" <?= ($staff['gender'] ?? '') == 'male' ? 'selected' : '' ?>>Male</option>
                            <option value="female" <?= ($staff['gender'] ?? '') == 'female' ? 'selected' : '' ?>>Female</option>
                            <option value="other" <?= ($staff['gender'] ?? '') == 'other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="dobirth">Date of Birth</label>
                        <input type="date" id="dobirth" name="dobirth" class="form-control" value="<?= esc($staff['dobirth'] ?? '') ?>">
                    </div>
                </div>

                <!-- Professional Information -->
                <h5 class="border-bottom pb-2 mb-3">Professional Information</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label" for="designation">Designation/Position</label>
                        <input type="text" id="designation" name="designation" class="form-control"
                               value="<?= esc($staff['designation']) ?>"
                               placeholder="e.g., Manager, Accountant, Sales Representative">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="contacts">Contact Information</label>
                        <textarea id="contacts" name="contacts" class="form-control" rows="2"
                                placeholder="Phone numbers, email addresses, etc."><?= esc($staff['contacts']) ?></textarea>
                    </div>
                </div>

                <!-- Additional Information -->
                <h5 class="border-bottom pb-2 mb-3">Additional Information</h5>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label" for="remarks">Remarks/Notes</label>
                        <textarea id="remarks" name="remarks" class="form-control" rows="3"
                                placeholder="Any additional information about this staff member"><?= esc($staff['remarks']) ?></textarea>
                    </div>
                </div>

                <!-- Photo Upload -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <label class="form-label" for="id_photo">ID Photo</label>
                        <?php if (!empty($staff['id_photo_path'])): ?>
                            <div class="mb-3">
                                <img src="<?= base_url($staff['id_photo_path']) ?>" alt="Staff ID Photo" class="img-thumbnail" style="max-height: 150px;">
                                <p class="small text-muted mt-2">Current ID photo</p>
                            </div>
                        <?php endif; ?>
                        <input type="file" id="id_photo" name="id_photo" class="form-control" accept="image/*">
                        <small class="text-muted">Maximum file size: 5MB. Accepted formats: JPG, PNG</small>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-end gap-2">
                    <a href="<?= base_url('smes/staff/' . $sme_id) ?>" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Staff Member</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>