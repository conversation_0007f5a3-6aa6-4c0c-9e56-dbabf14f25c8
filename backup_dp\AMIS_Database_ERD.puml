@startuml AMIS_Database_ERD
!theme plain
title AMIS Five - Database Entity Relationship Diagram

!define table(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define primary_key(x) <b>x</b>
!define foreign_key(x) <i>x</i>
!define column(x) x

' User Management Tables
table(users, "users\n====\nprimary_key(id): int\ncolumn(ucode): varchar\ncolumn(password): varchar\ncolumn(email): varchar\ncolumn(fname): varchar\ncolumn(lname): varchar\ncolumn(gender): enum\nforeign_key(branch_id): int\nforeign_key(report_to_id): int\ncolumn(role): enum\ncolumn(is_evaluator): tinyint\nforeign_key(commodity_id): int\ncolumn(user_status): tinyint")

table(branches, "branches\n====\nprimary_key(id): int\ncolumn(branch_code): varchar\ncolumn(branch_name): varchar\nforeign_key(province_id): int\nforeign_key(district_id): int\nforeign_key(llg_id): int\ncolumn(branch_status): tinyint")

table(gov_structure, "gov_structure\n====\nprimary_key(id): int\nforeign_key(parent_id): int\ncolumn(level): varchar\ncolumn(code): varchar\ncolumn(name): varchar\ncolumn(flag_filepath): varchar")

table(commodities, "commodities\n====\nprimary_key(id): int\ncolumn(commodity_code): varchar\ncolumn(commodity_name): varchar\ncolumn(commodity_icon): text\ncolumn(is_deleted): boolean")

' Workplan Management Tables
table(workplans, "workplans\n====\nprimary_key(id): int\ncolumn(workplan_code): varchar\ncolumn(workplan_title): varchar\ncolumn(workplan_description): text\ncolumn(objectives): text\ncolumn(start_date): date\ncolumn(end_date): date\nforeign_key(branch_id): int\nforeign_key(supervisor_id): int\nforeign_key(created_by): int")

table(workplan_activities, "workplan_activities\n====\nprimary_key(id): int\nforeign_key(workplan_id): int\ncolumn(activity_code): varchar\ncolumn(activity_title): varchar\ncolumn(activity_description): text\ncolumn(activity_type): enum\ncolumn(planned_start_date): date\ncolumn(planned_end_date): date\ncolumn(budget_allocated): decimal\ncolumn(location): varchar\ncolumn(gps_coordinates): varchar\nforeign_key(branch_id): int\nforeign_key(created_by): int")

table(workplan_training_activities, "workplan_training_activities\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\ncolumn(training_title): varchar\ncolumn(training_description): text\ncolumn(training_type): varchar\ncolumn(participants_target): int\ncolumn(venue): varchar\ncolumn(training_start_date): date\ncolumn(training_end_date): date\ncolumn(training_budget): decimal\ncolumn(facilitator): varchar\ncolumn(gps_coordinates): varchar\ncolumn(signing_sheet_filepath): varchar")

table(workplan_infrastructure_activities, "workplan_infrastructure_activities\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\ncolumn(infrastructure_title): varchar\ncolumn(infrastructure_description): text\ncolumn(infrastructure_type): varchar\ncolumn(location): varchar\ncolumn(gps_coordinates): varchar\ncolumn(estimated_cost): decimal\ncolumn(contractor): varchar\ncolumn(start_date): date\ncolumn(completion_date): date\ncolumn(signing_sheet_filepath): varchar")

table(workplan_input_activities, "workplan_input_activities\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\ncolumn(input_title): varchar\ncolumn(input_description): text\ncolumn(input_type): varchar\ncolumn(quantity): int\ncolumn(unit_of_measurement): varchar\ncolumn(unit_cost): decimal\ncolumn(total_cost): decimal\ncolumn(supplier): varchar\ncolumn(delivery_date): date\ncolumn(location): varchar\ncolumn(gps_coordinates): varchar\ncolumn(signing_sheet_filepath): varchar")

table(workplan_output_activities, "workplan_output_activities\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\ncolumn(output_title): varchar\ncolumn(output_description): text\ncolumn(output_type): varchar\ncolumn(quantity_target): int\ncolumn(unit_of_measurement): varchar\ncolumn(unit_value): decimal\ncolumn(total_value): decimal\ncolumn(target_completion_date): date\ncolumn(location): varchar\ncolumn(gps_coordinates): varchar\ncolumn(signing_sheet_filepath): varchar")

' Proposals Table
table(proposals, "proposals\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\ncolumn(proposal_code): varchar\ncolumn(proposal_title): varchar\ncolumn(proposal_description): text\ncolumn(proposal_status): enum\nforeign_key(supervisor_id): int\nforeign_key(action_officer_id): int\ncolumn(submitted_at): datetime\ncolumn(approved_at): datetime\ncolumn(supervisor_remarks): text\ncolumn(officer_remarks): text\ncolumn(me_rating): decimal\ncolumn(me_comments): text\ncolumn(me_rated_at): datetime\nforeign_key(me_rated_by): int")

' Strategic Plans Tables
table(plans_nasp, "plans_nasp\n====\nprimary_key(id): int\nforeign_key(parent_id): int\ncolumn(type): varchar\ncolumn(code): varchar\ncolumn(title): varchar\ncolumn(description): text\ncolumn(nasp_status): int")

table(plans_mtdp, "plans_mtdp\n====\nprimary_key(id): int\nforeign_key(parent_id): int\ncolumn(type): varchar\ncolumn(code): varchar\ncolumn(title): varchar\ncolumn(description): text\ncolumn(date_from): date\ncolumn(date_to): date\ncolumn(mtdp_status): int")

table(plans_corporate, "plans_corporate\n====\nprimary_key(id): int\nforeign_key(parent_id): int\ncolumn(type): varchar\ncolumn(code): varchar\ncolumn(title): varchar\ncolumn(description): text\ncolumn(date_from): date\ncolumn(date_to): date\ncolumn(corporate_status): int")

' Plan Linkage Tables
table(workplan_nasp_link, "workplan_nasp_link\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\nforeign_key(nasp_plan_id): int\nforeign_key(nasp_apa_id): int\nforeign_key(nasp_dip_id): int\nforeign_key(nasp_specific_area_id): int\nforeign_key(nasp_objective_id): int\nforeign_key(nasp_output_id): int\nforeign_key(nasp_indicator_id): int")

table(workplan_mtdp_link, "workplan_mtdp_link\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\nforeign_key(mtdp_plan_id): int\nforeign_key(mtdp_spa_id): int\nforeign_key(mtdp_dip_id): int\nforeign_key(mtdp_specific_area_id): int\nforeign_key(mtdp_investment_id): int\nforeign_key(mtdp_kra_id): int\nforeign_key(mtdp_strategy_id): int\nforeign_key(mtdp_indicator_id): int")

table(workplan_corporate_link, "workplan_corporate_link\n====\nprimary_key(id): int\nforeign_key(workplan_activity_id): int\nforeign_key(corporate_plan_id): int\nforeign_key(corporate_objective_id): int\nforeign_key(corporate_strategy_id): int\nforeign_key(corporate_kra_id): int\nforeign_key(corporate_indicator_id): int")

' Supporting Tables
table(meetings, "meetings\n====\nprimary_key(id): int\nforeign_key(branch_id): int\ncolumn(title): varchar\ncolumn(agenda): text\ncolumn(meeting_date): datetime\ncolumn(start_time): datetime\ncolumn(end_time): datetime\ncolumn(location): varchar\ncolumn(participants): json\ncolumn(status): enum\ncolumn(minutes): json\ncolumn(attachments): json")

table(documents, "documents\n====\nprimary_key(id): int\ncolumn(document_title): varchar\ncolumn(document_description): text\ncolumn(document_type): varchar\ncolumn(file_path): varchar\ncolumn(file_name): varchar\ncolumn(file_size): varchar\ncolumn(mime_type): varchar\nforeign_key(branch_id): int\ncolumn(document_status): enum")

table(agreements, "agreements\n====\nprimary_key(id): int\ncolumn(agreement_title): varchar\ncolumn(agreement_description): text\ncolumn(agreement_type): varchar\ncolumn(start_date): date\ncolumn(end_date): date\ncolumn(agreement_status): enum\ncolumn(file_path): varchar\nforeign_key(branch_id): int")

table(sme, "sme\n====\nprimary_key(id): int\nforeign_key(province_id): int\nforeign_key(district_id): int\nforeign_key(llg_id): int\ncolumn(village_name): varchar\ncolumn(sme_name): varchar\ncolumn(description): text\ncolumn(gps_coordinates): varchar\ncolumn(contact_details): text\ncolumn(logo_filepath): varchar\ncolumn(status): varchar")

' Relationships
users ||--o{ workplans : "creates/supervises"
users ||--o{ workplan_activities : "creates"
users ||--o{ proposals : "supervises/implements"
users }o--|| branches : "belongs_to"
users }o--|| commodities : "manages"
users }o--|| users : "reports_to"

branches }o--|| gov_structure : "located_in"
gov_structure ||--o{ gov_structure : "parent_child"

workplans ||--o{ workplan_activities : "contains"
workplans }o--|| branches : "belongs_to"
workplans }o--|| users : "supervised_by"

workplan_activities ||--o| workplan_training_activities : "details"
workplan_activities ||--o| workplan_infrastructure_activities : "details"
workplan_activities ||--o| workplan_input_activities : "details"
workplan_activities ||--o| workplan_output_activities : "details"
workplan_activities ||--o{ proposals : "generates"
workplan_activities }o--|| branches : "belongs_to"

workplan_activities ||--o| workplan_nasp_link : "linked_to"
workplan_activities ||--o| workplan_mtdp_link : "linked_to"
workplan_activities ||--o| workplan_corporate_link : "linked_to"

plans_nasp ||--o{ plans_nasp : "hierarchy"
plans_mtdp ||--o{ plans_mtdp : "hierarchy"
plans_corporate ||--o{ plans_corporate : "hierarchy"

workplan_nasp_link }o--|| plans_nasp : "references"
workplan_mtdp_link }o--|| plans_mtdp : "references"
workplan_corporate_link }o--|| plans_corporate : "references"

proposals }o--|| users : "supervised_by"
proposals }o--|| users : "implemented_by"
proposals }o--|| users : "rated_by"

meetings }o--|| branches : "belongs_to"
documents }o--|| branches : "belongs_to"
agreements }o--|| branches : "belongs_to"
sme }o--|| gov_structure : "located_in"

@enduml
