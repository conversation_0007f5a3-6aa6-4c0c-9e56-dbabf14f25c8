@startuml AMIS_System_Architecture
!theme plain
title AMIS Five - System Architecture Diagram

!define RECTANGLE class

package "Client Layer" {
    [Web Browser] as WB
    [Mobile Browser] as MB
}

package "Presentation Layer" {
    [Bootstrap 5.3.0] as BS
    [jQuery 3.6.0] as JQ
    [DataTables 1.13.6] as DT
    [Select2 4.1.0] as S2
    [Chart.js] as CJ
    [Font Awesome 6.4.0] as FA
    [OpenStreetMap] as OSM
    [Toastr Notifications] as TN
}

package "Web Server Layer - Apache (XAMPP)" {
    [Apache Web Server] as WS
    [PHP 8+] as PHP
    [CodeIgniter 4 Framework] as CI4
}

package "Application Layer - MVC Architecture" {
    package "Controllers" {
        [Home Controller] as HC
        [Dashboard Controller] as DC
        [Workplan Controller] as WC
        [Activities Controller] as AC
        [Proposals Controller] as PC
        [Reports Controller] as RC
        [Admin Controllers] as ADC
        [AJAX Controller] as AJAXC
    }
    
    package "Models" {
        [User Model] as UM
        [Workplan Model] as WM
        [Activity Model] as AM
        [Proposal Model] as PM
        [NASP Model] as NM
        [MTDP Model] as MM
        [Corporate Model] as CM
        [Government Structure Model] as GSM
    }
    
    package "Views" {
        [System Template] as ST
        [Workplan Views] as WV
        [Activity Views] as AV
        [Proposal Views] as PV
        [Report Views] as RV
        [Admin Views] as ADV
    }
}

package "Security Layer" {
    [Auth Filter] as AF
    [CSRF Protection] as CSRF
    [Secure Headers] as SH
    [Role-Based Access Control] as RBAC
    [Input Validation] as IV
}

package "Data Layer" {
    database "MySQL Database\n(amis_db)" as DB {
        [users] as UT
        [workplans] as WT
        [workplan_activities] as AT
        [proposals] as PT
        [plans_nasp] as NT
        [plans_mtdp] as MT
        [plans_corporate] as CT
        [gov_structure] as GT
        [branches] as BT
        [commodities] as COMT
    }
}

package "File Storage" {
    folder "public/uploads/" as UF
    folder "public/assets/" as AS
    folder "writable/session/" as SS
    folder "writable/cache/" as CS
    folder "writable/logs/" as LS
}

package "External Services" {
    [SMTP Server\ndakoiims.com:465] as SMTP
    [OpenStreetMap API] as MAP
}

' Client connections
WB --> WS
MB --> WS

' Web server connections
WS --> PHP
PHP --> CI4

' Framework to controllers
CI4 --> HC
CI4 --> DC
CI4 --> WC
CI4 --> AC
CI4 --> PC
CI4 --> RC
CI4 --> ADC
CI4 --> AJAXC

' Controllers to models
HC --> UM
WC --> WM
AC --> AM
PC --> PM
RC --> NM
RC --> MM
RC --> CM
ADC --> GSM

' Models to database
UM --> DB
WM --> DB
AM --> DB
PM --> DB
NM --> DB
MM --> DB
CM --> DB
GSM --> DB

' Security layer
CI4 --> AF
CI4 --> CSRF
CI4 --> SH
CI4 --> RBAC
CI4 --> IV

' Views layer
CI4 --> ST
WC --> WV
AC --> AV
PC --> PV
RC --> RV
ADC --> ADV

' File storage
CI4 --> UF
CI4 --> AS
CI4 --> SS
CI4 --> CS
CI4 --> LS

' External services
CI4 --> SMTP
RV --> MAP

' Presentation layer
ST --> BS
ST --> JQ
ST --> DT
ST --> S2
ST --> CJ
ST --> FA
ST --> TN
RV --> OSM

@enduml
