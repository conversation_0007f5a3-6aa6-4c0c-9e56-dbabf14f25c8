@startuml AMIS_System_Integration
!theme plain
title AMIS Five - System Integration Diagram

!define RECTANGLE class

package "AMIS Core System" {
    component [AMIS Application\nCodeIgniter 4] as AMISCore
    component [User Management\nModule] as UserMgmt
    component [Workplan Management\nModule] as WorkplanMgmt
    component [Activity Management\nModule] as ActivityMgmt
    component [Proposal Management\nModule] as ProposalMgmt
    component [Reports Module] as ReportsMgmt
    component [Admin Module] as AdminMgmt
    
    database "AMIS Database\n(MySQL)" as AMISDatabase {
        [User Data] as UserData
        [Workplan Data] as WorkplanData
        [Activity Data] as ActivityData
        [Strategic Plans Data] as PlansData
        [System Configuration] as ConfigData
    }
}

package "Authentication & Security" {
    component [Authentication Service] as AuthService
    component [Session Management] as SessionMgmt
    component [Role-Based Access Control] as RBAC
    component [Security Filters] as SecurityFilters
    component [CSRF Protection] as CSRFProtection
}

package "Email Integration" {
    component [Email Service\nCodeIgniter Email] as EmailService
    component [SMTP Server\ndakoiims.com] as SMTPServer
    component [Email Templates] as EmailTemplates
    component [Notification Queue] as NotificationQueue
}

package "File Management" {
    component [File Upload Service] as FileUpload
    component [Document Storage] as DocumentStorage
    component [Image Processing] as ImageProcessing
    component [File Security] as FileSecurity
    
    folder "File Storage" {
        folder "public/uploads/" as UploadsFolder
        folder "public/assets/" as AssetsFolder
        folder "writable/logs/" as LogsFolder
        folder "writable/cache/" as CacheFolder
    }
}

package "External APIs & Services" {
    cloud "OpenStreetMap" {
        [Map Tiles API] as MapTiles
        [Geocoding API] as Geocoding
        [Routing API] as Routing
    }
    
    cloud "Chart.js Library" {
        [Chart Rendering] as ChartRendering
        [Data Visualization] as DataVisualization
    }
    
    cloud "DataTables Library" {
        [Table Processing] as TableProcessing
        [PDF Export] as PDFExport
        [Excel Export] as ExcelExport
    }
    
    cloud "Select2 Library" {
        [Enhanced Dropdowns] as EnhancedDropdowns
        [AJAX Search] as AJAXSearch
    }
}

package "Frontend Integration" {
    component [Bootstrap Framework] as Bootstrap
    component [jQuery Library] as jQuery
    component [JavaScript Modules] as JSModules
    component [CSS Styling] as CSSStyles
    component [Font Awesome Icons] as FontAwesome
    component [Toastr Notifications] as ToastrNotifications
}

package "Reporting & Analytics" {
    component [Report Generator] as ReportGenerator
    component [Data Aggregation] as DataAggregation
    component [Chart Generation] as ChartGeneration
    component [Export Services] as ExportServices
    component [Activity Maps] as ActivityMaps
}

package "Strategic Planning Integration" {
    component [NASP Integration] as NASPIntegration
    component [MTDP Integration] as MTDPIntegration
    component [Corporate Plan Integration] as CorporateIntegration
    component [Plan Linkage Service] as PlanLinkage
}

package "Government Structure" {
    component [Hierarchical Data Service] as HierarchicalService
    component [Location Services] as LocationServices
    component [Branch Management] as BranchMgmt
    component [Regional Data] as RegionalData
}

package "Commodity Management" {
    component [Commodity Service] as CommodityService
    component [Price Tracking] as PriceTracking
    component [Production Data] as ProductionData
    component [Market Analysis] as MarketAnalysis
}

package "Monitoring & Evaluation" {
    component [M&E Service] as MEService
    component [Activity Rating] as ActivityRating
    component [Performance Metrics] as PerformanceMetrics
    component [Evaluation Reports] as EvaluationReports
}

' Core System Connections
AMISCore --> UserMgmt
AMISCore --> WorkplanMgmt
AMISCore --> ActivityMgmt
AMISCore --> ProposalMgmt
AMISCore --> ReportsMgmt
AMISCore --> AdminMgmt

UserMgmt --> UserData
WorkplanMgmt --> WorkplanData
ActivityMgmt --> ActivityData
ProposalMgmt --> ActivityData
ReportsMgmt --> PlansData
AdminMgmt --> ConfigData

' Authentication & Security Connections
AMISCore --> AuthService
AuthService --> SessionMgmt
AuthService --> RBAC
AMISCore --> SecurityFilters
SecurityFilters --> CSRFProtection

' Email Integration Connections
AMISCore --> EmailService
EmailService --> SMTPServer
EmailService --> EmailTemplates
EmailService --> NotificationQueue

' File Management Connections
AMISCore --> FileUpload
FileUpload --> DocumentStorage
FileUpload --> ImageProcessing
FileUpload --> FileSecurity
DocumentStorage --> UploadsFolder
DocumentStorage --> AssetsFolder
AMISCore --> LogsFolder
AMISCore --> CacheFolder

' External APIs Connections
ReportsMgmt --> MapTiles
ActivityMaps --> Geocoding
ActivityMaps --> Routing
ReportsMgmt --> ChartRendering
ChartGeneration --> DataVisualization
ReportsMgmt --> TableProcessing
ExportServices --> PDFExport
ExportServices --> ExcelExport
AMISCore --> EnhancedDropdowns
AMISCore --> AJAXSearch

' Frontend Integration Connections
AMISCore --> Bootstrap
AMISCore --> jQuery
AMISCore --> JSModules
AMISCore --> CSSStyles
AMISCore --> FontAwesome
AMISCore --> ToastrNotifications

' Reporting & Analytics Connections
ReportsMgmt --> ReportGenerator
ReportGenerator --> DataAggregation
ReportGenerator --> ChartGeneration
ReportGenerator --> ExportServices
ReportsMgmt --> ActivityMaps

' Strategic Planning Connections
ActivityMgmt --> NASPIntegration
ActivityMgmt --> MTDPIntegration
ActivityMgmt --> CorporateIntegration
NASPIntegration --> PlanLinkage
MTDPIntegration --> PlanLinkage
CorporateIntegration --> PlanLinkage

' Government Structure Connections
AdminMgmt --> HierarchicalService
UserMgmt --> LocationServices
AdminMgmt --> BranchMgmt
ReportsMgmt --> RegionalData

' Commodity Management Connections
AdminMgmt --> CommodityService
CommodityService --> PriceTracking
CommodityService --> ProductionData
ReportsMgmt --> MarketAnalysis

' M&E Connections
ProposalMgmt --> MEService
MEService --> ActivityRating
MEService --> PerformanceMetrics
ReportsMgmt --> EvaluationReports

note right of AMISCore
  Core Integration Points:
  - RESTful API endpoints
  - AJAX data exchange
  - Session-based authentication
  - Role-based access control
  - Email notifications
  - File upload/download
  - Real-time data updates
end note

note right of SMTPServer
  Email Configuration:
  - Server: mail.dakoiims.com
  - Port: 465 (SSL)
  - Authentication: SMTP
  - Notifications: Automated
  - Templates: HTML/Text
end note

note right of UploadsFolder
  File Storage:
  - User photos
  - Activity documents
  - Signing sheets
  - Report exports
  - System backups
  - Security: Access controlled
end note

@enduml
