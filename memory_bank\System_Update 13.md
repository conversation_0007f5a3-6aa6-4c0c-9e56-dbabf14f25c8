- New Activity Type
- Add Output as one activity - new activity Type (item description, quantity, unit, date, remarks)
- Linking activity indicator
- After creating activitiy redirect to link activity
- Add Others link in the activities linking. Others refers to activitys not captured in the plan like recurrent activities
- If an activity is not linked, it cannot be assigned to a user
- <PERSON>min should view all the supervisors activities as well as officers activities
- Add PDF Print function in all the activity Pages - fix layout for document
- Reports: MTDP Report add filters: SPA, DIP, Specific Area, Date from - Date to. Dynamic Graphs and charts display according to the filter. Same applies to NASP
- Add PDF Export to Reports All reports pages
- Add HR Reports - Gender, Date Joined etc...
- Add Report for Gov. Structure 
- Add SME Reports - SME can be displayed on the Map - gps locations. use filter, district filter
- Add Price Report trends for commodity boards too
- 

- Update the Corporate Plan Structure
- Adding Output as new activity Type
- Update Create Activity function to: After activity is created, it displays the linking page so the user can link the activity to NASP, MTDP, and other plans
- Add another linking option call Others, this will apply to activities that are not linked to the mentioned plans, like recurrent activities
- Add restriction and indicator, if an activity is not linked to a plan or others the Supervisor will not be able to assign create proposal for it and assign it to a user
- Update Admin Restrictions/Permissions to the Admin should see all the activities for all the users
- Add Filters for the Reports: Date Range Filters and each sections according to the nature of each report to make the report dynamically display charts and graphs according to the filteration
- Add Export to PDF Feature to all the Reports for ease of report printing and report layout
- Add SMEs onto the map display
- 