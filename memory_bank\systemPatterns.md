# System Patterns and Architecture for Cursor AI

This file documents the system architecture, key technical decisions, design patterns in use, relationships between components, and critical implementation paths for Cursor AI's reference.

Content to be filled by the user:
- Overview of system architecture (e.g., monolithic, microservices, MVC, client-server)
- Diagrams or links to diagrams (if available)
- Key technical decisions made and their rationale
- Design patterns consistently used (e.g., Singleton, Factory, Observer)
- How major components/modules interact
- Data flow diagrams or descriptions
- Critical implementation paths or core algorithms 