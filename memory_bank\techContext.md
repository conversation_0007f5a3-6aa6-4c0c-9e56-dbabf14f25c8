# Technical Context for Cursor AI

This document outlines the technologies used in the project, development setup instructions, technical constraints, dependencies, and common tool usage patterns to guide Cursor AI.

Content to be filled by the user:
- Programming languages and versions (e.g., PHP 8.1, JavaScript ES2022)
- Frameworks and libraries (e.g., CodeIgniter 4, jQuery 3.x, React)
- Database system (e.g., MySQL, PostgreSQL, MongoDB)
- Key external APIs or services used
- Development environment setup (e.g., XAMPP, Docker, specific IDE extensions)
- Build tools, linters, formatters (e.g., Composer, ESLint, Prettier)
- Version control system and branching strategy (e.g., Git, Gitflow)
- Technical constraints (e.g., performance targets, browser compatibility)
- Important dependencies and their versions
- Common tool usage patterns or preferred tools for specific tasks 